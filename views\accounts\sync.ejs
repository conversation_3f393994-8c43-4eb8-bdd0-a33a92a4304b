<%- contentFor('body') %>
<div class="sync-account-container">
    <div class="page-header fade-in">
        <h1 class="section-title">同步账号</h1>
        <a href="/accounts" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> 返回账号列表
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="sync-account-form glass-card">
                <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <%= error %>
                </div>
                <% } %>

                <div class="sync-description">
                    <h2><i class="bi bi-arrow-repeat"></i> 同步账号</h2>
                    <p>此操作将从Cloudflare获取所有邮件路由规则，并与本地数据库同步。</p>
                    <p>同步过程中：</p>
                    <ul>
                        <li>Cloudflare中存在但本地不存在的账号将被添加到本地</li>
                        <li>本地存在但Cloudflare中不存在的账号将被从本地删除</li>
                        <li>没有密码的账号将自动生成新密码</li>
                        <li>账号状态将与Cloudflare路由规则的启用状态同步</li>
                    </ul>
                </div>

                <div id="sync-progress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-status">正在同步...</div>
                </div>

                <div id="sync-result" style="display: none;">
                    <div class="result-summary alert alert-success">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <span class="summary-text">同步完成！</span>
                    </div>
                    <div class="result-details">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="added-accounts result-card">
                                    <h4><i class="bi bi-plus-circle"></i> 新增账号</h4>
                                    <ul class="added-list"></ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="removed-accounts result-card">
                                    <h4><i class="bi bi-trash"></i> 删除账号</h4>
                                    <ul class="removed-list"></ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="updated-password-accounts result-card">
                                    <h4><i class="bi bi-key"></i> 更新密码</h4>
                                    <ul class="updated-password-list"></ul>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="updated-status-accounts result-card">
                                    <h4><i class="bi bi-toggle-on"></i> 更新状态</h4>
                                    <ul class="updated-status-list"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button id="start-sync-btn" class="btn btn-primary">
                        <i class="bi bi-arrow-repeat"></i> 开始同步
                    </button>
                    <a href="/accounts" id="back-btn" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    <a href="/accounts" id="done-btn" class="btn btn-success" style="display: none;">
                        <i class="bi bi-check-circle"></i> 完成
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="sync-account-info glass-card">
                <h2 class="info-title"><i class="bi bi-info-circle"></i> 同步说明</h2>
                <div class="info-content">
                    <p>同步功能可以确保本地数据库与Cloudflare上的邮件路由规则保持一致。</p>
                    <h3>注意事项：</h3>
                    <ul>
                        <li>同步过程可能需要几秒钟时间，请耐心等待</li>
                        <li>同步会删除本地存在但Cloudflare中不存在的账号</li>
                        <li>新增的账号将自动生成随机密码</li>
                        <li>没有密码的账号将自动生成新密码</li>
                        <li>账号状态将与Cloudflare路由规则的启用状态同步</li>
                        <li>同步不会影响Cloudflare上的邮件路由规则</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<%- contentFor('style') %>
<!-- 使用外部CSS文件 -->

<%- contentFor('script') %>
<!-- 使用外部JS文件 -->
