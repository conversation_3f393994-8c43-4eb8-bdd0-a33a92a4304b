# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Build output
dist/
build/
out/

# Environment variables and config
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production
*.config.js

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - JetBrains
.idea/
*.iml
*.iws
*.ipr

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Operating System
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.swp
*.swo
*.tmp
*.temp
*.bak

# Coverage and test
coverage/
.nyc_output/
.jest/

# Data
data/

# Config
config.yaml
