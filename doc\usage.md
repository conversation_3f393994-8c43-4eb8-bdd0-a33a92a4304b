# CloudflareMailServer 使用说明

本文档提供了CloudflareMailServer系统的基本使用指南，包括系统概述、功能说明和常见操作流程。

## 目录

- [系统概述](#系统概述)
- [前提条件](#前提条件)
- [基本功能](#基本功能)
- [管理员操作指南](#管理员操作指南)
  - [登录系统](#登录系统)
  - [账号管理](#账号管理)
  - [邮件查看](#邮件查看)
  - [系统设置](#系统设置)
- [常见问题](#常见问题)

## 系统概述

CloudflareMailServer是一个基于Cloudflare Email Routing和Node.js的开源企业邮箱管理平台。该系统允许管理员通过简洁的Web界面管理多个邮箱账号，并直接通过IMAP协议查看和处理这些邮箱的邮件。系统不存储邮件内容，而是作为一个代理，直接连接到第三方邮件服务（如Gmail）来获取和显示邮件。

主要特点：
- 基于Cloudflare Email Routing的邮件转发
- 直接通过IMAP查看邮件，不存储邮件内容
- 支持多域名管理
- 提供Web界面和API接口
- 使用SQLite数据库进行本地数据存储

## 前提条件

使用本系统前，您需要满足以下条件：

1. **Cloudflare账户**：
   - 已验证的域名
   - Email Routing功能已启用
   - 具有适当权限的API令牌

2. **Gmail或其他IMAP邮箱**：
   - 用于接收转发的邮件
   - IMAP访问已启用
   - 应用专用密码（如果启用了两步验证）

3. **服务器环境**：
   - Node.js v16+
   - 网络连接

## 基本功能

CloudflareOpenMail系统提供以下核心功能：

### 1. 账号管理

- **账号列表**：查看所有邮箱账号
- **创建账号**：手动或自动创建新邮箱账号
- **编辑账号**：修改账号信息和状态
- **删除账号**：删除不再需要的邮箱账号
- **重置密码**：重置账号密码
- **同步账号**：与Cloudflare同步账号信息

### 2. 邮件处理

- **邮件列表**：查看所有邮件或特定账号的邮件
- **邮件详情**：查看邮件内容和附件
- **标记已读**：标记邮件为已读
- **拉取邮件**：手动触发邮件拉取
- **邮件过滤**：按发件人、主题、日期等过滤邮件

### 3. 系统管理

- **用户认证**：基于JWT的用户认证
- **活动日志**：记录系统活动
- **配置管理**：通过配置文件管理系统设置

## 管理员操作指南

### 登录系统

1. 打开浏览器，访问系统URL（例如：`http://your-server:3116`）
2. 在登录页面输入管理员用户名和密码
   - 默认用户名和密码在`config.yaml`文件中配置
3. 点击"登录"按钮
4. 登录成功后，将进入系统仪表盘

### 账号管理

#### 查看账号列表

1. 在左侧导航菜单中点击"账号管理"
2. 系统将显示所有邮箱账号的列表
3. 可以使用搜索框搜索特定账号
4. 可以按状态（活跃/非活跃）过滤账号
5. 可以按创建时间或上次访问时间排序

#### 创建新账号

##### 自动生成账号

1. 在账号列表页面，点击"自动生成账号"按钮
2. 在弹出的对话框中选择域名（如果有多个域名）
3. 点击"生成"按钮
4. 系统将自动生成用户名和密码，并创建账号
5. 创建成功后，记录生成的账号信息

##### 手动创建账号

1. 在账号列表页面，点击"创建账号"按钮
2. 在弹出的表单中填写以下信息：
   - 用户名：邮箱前缀
   - 域名：从下拉列表中选择
   - 密码：可选，如果不填写将自动生成
3. 点击"创建"按钮
4. 创建成功后，记录账号信息

#### 编辑账号

1. 在账号列表中找到要编辑的账号
2. 点击该账号行右侧的"编辑"按钮
3. 在弹出的表单中修改以下信息：
   - 用户名
   - 密码
   - 状态（活跃/非活跃）
4. 点击"保存"按钮应用更改

#### 删除账号

1. 在账号列表中找到要删除的账号
2. 点击该账号行右侧的"删除"按钮
3. 在确认对话框中点击"确认"
4. 系统将从Cloudflare和本地数据库中删除该账号

#### 重置账号密码

1. 在账号列表中找到要重置密码的账号
2. 点击该账号行右侧的"更多"按钮，然后选择"重置密码"
3. 在确认对话框中点击"确认"
4. 系统将生成新密码并显示
5. 记录新密码，因为它只会显示一次

#### 同步账号

1. 在账号列表页面，点击"同步账号"按钮
2. 系统将与Cloudflare同步账号信息
3. 同步完成后，将显示同步结果（添加、更新、删除的账号数量）

### 邮件查看

#### 查看邮件列表

1. 在左侧导航菜单中点击"邮件管理"
2. 系统将显示所有邮件的列表
3. 可以使用以下过滤条件：
   - 账号：选择特定邮箱账号
   - 发件人：按发件人过滤
   - 主题：按主题关键词过滤
   - 日期范围：按日期范围过滤
   - 未读邮件：只显示未读邮件
4. 点击"应用过滤"按钮更新列表

#### 查看邮件详情

1. 在邮件列表中点击要查看的邮件
2. 系统将显示邮件详情，包括：
   - 发件人信息
   - 收件人信息
   - 主题
   - 日期和时间
   - 邮件内容
   - 附件列表（如果有）
3. 邮件将自动标记为已读

#### 标记邮件为已读

1. 在邮件列表中，可以通过以下方式标记邮件为已读：
   - 单个邮件：点击邮件行右侧的"标记为已读"按钮
   - 批量标记：选择多个邮件，然后点击"标记为已读"按钮

#### 拉取新邮件

1. 在邮件列表页面，点击"检查新邮件"按钮
2. 系统将开始从IMAP服务器拉取新邮件
3. 可以通过进度指示器查看拉取进度
4. 拉取完成后，列表将自动更新

### 系统设置

系统设置主要通过`config.yaml`文件进行配置，包括：

- 服务器设置
- 代理设置
- Cloudflare配置
- 邮件服务配置
- 日志配置
- 管理员账号
- JWT认证配置
- 登录限制配置

详细配置说明请参考[README.md](../README.md#配置说明)。

## 常见问题

### 1. 无法登录系统

可能的原因：
- 用户名或密码错误
- 登录尝试次数过多，账号被锁定
- 配置文件中的管理员凭据不正确

解决方法：
- 检查用户名和密码
- 等待锁定时间结束后重试
- 检查`config.yaml`文件中的管理员配置

### 2. 无法创建账号

可能的原因：
- Cloudflare API令牌权限不足
- Cloudflare API令牌已过期
- 域名未在Cloudflare中验证
- Email Routing功能未启用

解决方法：
- 检查API令牌权限
- 创建新的API令牌
- 确认域名已验证
- 在Cloudflare中启用Email Routing

### 3. 无法拉取邮件

可能的原因：
- IMAP配置不正确
- IMAP服务器不可用
- 邮箱密码或应用专用密码错误
- 网络连接问题

解决方法：
- 检查IMAP配置
- 确认IMAP服务器状态
- 更新邮箱密码或应用专用密码
- 检查网络连接和代理设置

### 4. 系统性能问题

可能的原因：
- 邮件数量过多
- 服务器资源不足
- 数据库文件过大

解决方法：
- 优化邮件过滤条件
- 增加服务器资源
- 定期备份和清理数据库

### 5. 安全问题

建议：
- 定期更改管理员密码
- 使用强密码
- 定期更新API令牌
- 限制系统访问IP
- 使用HTTPS保护通信
- 定期备份数据
