.view-account-container {
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.account-details,
.account-sidebar {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    opacity: 0;
    animation: fadeIn 0.8s ease-in forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.account-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.account-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.account-title h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    word-break: break-all;
}

.account-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    text-decoration: none;
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.copy-email-btn {
    color: var(--primary-color);
}

.copy-password-btn {
    color: var(--secondary-color);
}

.reset-password-btn {
    color: var(--warning-color);
}

.view-mail-btn {
    color: #6c5ce7; /* 紫色 */
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-inactive {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.account-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-group {
    margin-bottom: 1rem;
}

.info-group label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.5rem;
}

.info-value {
    font-size: 1rem;
    color: white;
    word-break: break-all;
}

.password-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.password-value {
    font-family: monospace;
    letter-spacing: 1px;
}

.notes-field {
    white-space: pre-line;
    min-height: 3rem;
}

.account-footer {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.account-sidebar h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar-content {
    color: rgba(255, 255, 255, 0.8);
}

.sidebar-content h4 {
    font-size: 1.1rem;
    margin: 1.5rem 0 1rem;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-block {
    display: block;
    width: 100%;
    text-align: center;
}

.new-password-container {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
}

.password-note {
    color: var(--warning-color);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Toast消息 */
.toast-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.toast-message.show {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        width: 100%;
    }
    
    .header-actions .btn {
        flex: 1;
        text-align: center;
    }
    
    .account-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .account-actions {
        align-self: flex-end;
    }
    
    .account-info {
        grid-template-columns: 1fr;
    }
    
    .account-footer {
        flex-direction: column;
    }
    
    .account-footer .btn {
        width: 100%;
    }
}
